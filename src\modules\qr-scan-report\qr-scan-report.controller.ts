import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { QrScanReportAction } from './actions';
import { QrScanReportService } from './qr-scan-report.service';
import { JwtGuard } from '@la-pasta-module/auth';


@ApiTags('QR Scan Report')
@Controller('qr-scan-report')
@UseGuards(JwtGuard)
export class QrScanReportController {
    constructor(private readonly qrScanReportService: QrScanReportService) { }

    // @Get('')
    // async getQrScanReport(@Query() query: QueryFilter) {
    //     return await this.qrScanReportService.getQrScanReport(query);
    // }

    @ApiOperation({ summary: 'Get QR scan report for particulars' })
    @ApiResponse({ status: 200, description: 'Returns the QR scan report for particulars' })
    @Get('particular')
    async getQrScanReportParticular(@Query() query: QueryFilter) {
        return await this.qrScanReportService.getQrScanReportParticular(query);
    }
}
