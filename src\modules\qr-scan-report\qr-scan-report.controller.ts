import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { QrScanReportAction } from './actions';
import { QrScanReportService } from './qr-scan-report.service';
import { JwtGuard } from '@la-pasta-module/auth';


@Controller('qr-scan-report')
@UseGuards(JwtGuard)
export class QrScanReportController {
    constructor(private readonly qrScanReportService: QrScanReportService) { }

    // @Get('')
    // async getQrScanReport(@Query() query: QueryFilter) {
    //     return await this.qrScanReportService.getQrScanReport(query);
    // }

    @Get('qr-scan-report-particular')
    async getQrScanReportParticular(@Query() query: QueryFilter) {
        return await this.qrScanReportService.getQrScanReportParticular(query);
    }
}
