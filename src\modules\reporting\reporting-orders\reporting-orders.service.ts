import { Order, OrderRepository, OrderStatus, PaymentMode } from '@la-pasta-module/order';
import { BaseService, ReportingHelpers, convertFilter, convertParams, t } from '@la-pasta/common';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { OrderRetailRepository } from '@la-pasta-module/order-retaill/repository';
import { CompanyCategory } from '@la-pasta-module/companies';
import { UserRepository } from '@la-pasta-module/users';
import moment from 'moment';
import { ObjectId } from 'bson';
import { ReloadBalanceRepository } from '@la-pasta-module/reload-balance/repository';
import { OrderSupplierRepository } from '@la-pasta-module/order-supplier/repository';
import { ScannerDataRepository } from '@la-pasta-module/scanner-data/repository';


@Injectable()
export class ReportingOrdersService extends BaseService {

    constructor(
        private userRepository: UserRepository,
        private orderRepository: OrderRepository,
        private orderRetailRepository: OrderRetailRepository,
        private orderSupplierRepository: OrderSupplierRepository,
        private scannerDataRepository: ScannerDataRepository
    ) {
        super(orderRepository)
    }

    async getPayementMode(query) {
        query = convertFilter(query);
        const sommePayementMode = await this.orderRepository.findModePayment(query);
        const valueReturn = sommePayementMode.reduce((accumulator, currentValue) => accumulator + currentValue?.total, 0)

        sommePayementMode.forEach(paymentMode => {
            paymentMode.percentage = Number(this.calculpercent(paymentMode?.total, valueReturn))
        })

        return sommePayementMode;
    }

    async getCimencamMode(query) {
        query = convertFilter(query);
        const sommePayementcimenMode = await this.orderRepository.findModeCimencam(query);
        const valueReturn = sommePayementcimenMode.reduce((accumulator, currentValue) => accumulator + currentValue?.total, 0)

        sommePayementcimenMode.forEach(paymentMode => {
            paymentMode.percentage = Number(this.calculpercent(paymentMode?.total, valueReturn))

        })
        return sommePayementcimenMode;
    }

    async getSalesEvolutionByUsers(filter: QueryFilter) {
        const isDoughnut = filter?.isDoughnut || null;
        delete filter?.isDoughnut;

        filter = convertFilter(filter);
        filter['company'] = { $exists: 1 };
        const data = await this.orderRepository.getEvolutionOrders(filter) as any[];
        const dataChart = {};
        const array = Object.values(CompanyCategory).filter(key => typeof key === 'string');
        array.forEach(key => dataChart[key] = data.filter(company => company.category == CompanyCategory[key]));

        if (isDoughnut && isDoughnut == 'true') {
            return this.orderRepository.getVolumeOrderByUsers(filter);
        }

        return this.generateChartYearData(dataChart);
    }

    getMonthNumber(dateString) {

        const date = moment(dateString, "YYYY-MM");
        return date.month() + 1;
    }

    async getEvolutionProduct(filter: QueryFilter) {
        const isDoughnut = filter?.isDoughnut || null;
        delete filter?.isDoughnut;

        filter = this.convertFilter(filter);

        const data = await this.orderRepository.getEvolutionProduct(filter) as QueryFilter[];
        const dataChart = {};
        data?.forEach(elt => {
            dataChart[elt?.label] = data?.filter(item => item?.label === elt?.label);
        })

        if (isDoughnut && isDoughnut == 'true') {
            return this.orderRepository.getRepartitionProduct(filter);
        }

        return this.generateChartYearData(dataChart);
    }

    async getEvolutionProductParticularByScannerOrder(filter: QueryFilter) {
        const isDoughnut = filter?.isDoughnut || null;
        delete filter?.isDoughnut;

        filter = this.convertFilter(filter);

        if (isDoughnut && isDoughnut == 'true') {
            return this.scannerDataRepository.getEvolutionOrderByScannerOrder(filter);
        }

        const data = await this.scannerDataRepository.getEvolutionOrderByScannerOrder(filter) as QueryFilter[];

        return this.formatChartData(data);
    }

    async getEvolutionProductParticularByManualOrderMethod(filter: QueryFilter) {
        const isDoughnut = filter?.isDoughnut || null;
        delete filter?.isDoughnut;

        filter = this.convertFilter(filter);

        const data = await this.orderSupplierRepository.getEvolutionProduct(filter) as QueryFilter[];

        if (isDoughnut && isDoughnut == 'true') {
            return this.orderSupplierRepository.getRepartitionProduct(filter);
        }

        return this.formatChartData(data);
    }

    // Méthode utilitaire pour générer les données du graphique
    private formatChartData(data: QueryFilter[]) {
        const dataChart = {};
        data?.forEach(elt => {
            dataChart[elt?.label] = data?.filter(item => item?.label === elt?.label);
        });
        return this.generateChartYearData(dataChart);
    }

    async getProductTotal(filter: QueryFilter) {

        const isDoughnut = filter?.isDoughnut || null;
        delete filter?.isDoughnut;

        filter = this.convertFilter(filter);

        const data = await this.orderRepository.getEvolutionProduct(filter) as QueryFilter[];
        const dataChart = {};

        data?.forEach(elt => {
            if (!dataChart[elt.label]) {
                dataChart[elt.label] = Array(12).fill(0); // Initialiser les mois avec des 0
            }
            const monthIndex = parseInt(elt.monthOfYear) - 1; // Convertir le mois (01-12) en index (0-11)
            dataChart[elt.label][monthIndex] += elt.quantity;

        })

        if (isDoughnut && isDoughnut == 'true') {
            return this.orderRepository.getRepartitionProduct(filter);
        }

        return dataChart;
    }

    async getProductTotalParticular(filter: QueryFilter) {

        const isDoughnut = filter?.isDoughnut || null;
        delete filter?.isDoughnut;

        filter = this.convertFilter(filter);

        const data = await this.scannerDataRepository.getEvolutionProduct(filter) as QueryFilter[];
        const dataChart = {};

        data?.forEach(elt => {
            if (!dataChart[elt.label]) {
                dataChart[elt.label] = Array(12).fill(0); // Initialiser les mois avec des 0
            }
            const monthIndex = parseInt(elt.monthOfYear) - 1; // Convertir le mois (01-12) en index (0-11)
            dataChart[elt.label][monthIndex] += elt.quantity;

        })

        if (isDoughnut && isDoughnut == 'true') {
            return this.scannerDataRepository.getRepartitionProduct(filter);
        }

        return dataChart;
    }

    async getExportRecapOrder(filter: QueryFilter) {
        try {
            filter = convertFilter(filter);
            const res = await this.orderRepository.getExportRecapOrder(filter) as QueryFilter[];
            return res
        } catch (error) {
            return error;
        }
    }

    async getEvolutionSalePayment(filter: QueryFilter) {
        filter = this.convertFilter(filter);
        const data = await this.orderRepository.getEvolutionSalePayment(filter) as QueryFilter[];
        return this.generateChartYearDataValue({ dataSaleEvolutions: data })?.dataSaleEvolutions;
    }

    async getEvolutionSalePaymentParticular(filter: QueryFilter) {
        filter = this.convertFilter(filter);
        const data = await this.scannerDataRepository.getEvolutionSalePaymentForParticular(filter) as QueryFilter[];
        return this.generateChartYearDataValue({ dataSaleEvolutions: data })?.dataSaleEvolutions;
    }

    async getEvolutionRegion(query: QueryFilter) {
        const isDoughnut = query?.isDoughnut || null;
        delete query?.isDoughnut;

        query = convertFilter(query);
        const res = await this.orderRepository.getEvolutionRegion(query);
        const dataChart = { 'LSO': [], 'GNO': [], 'GNO 1': [], 'GNO 2': [], 'CS': [], 'ONO': [], };

        res.forEach(elt => {
            dataChart[elt?.cart?.store?.address?.commercialRegion]?.push(elt)
        })

        if (isDoughnut && isDoughnut == 'true') {
            return await this.orderRepository.getRepartitionRegion(query);
        }

        return this.generateChartYearData(dataChart);
    }

    async getEvolutionPayement(query: QueryFilter) {
        const isDoughnut = query?.isDoughnut || null;
        delete query?.isDoughnut;

        query = convertFilter(query);
        const data = await this.orderRepository.getEvolutionPayement(query) as Order[];
        const dataChart = {};
        const paymentMode = Object.values(PaymentMode).filter(key => typeof key === 'string');

        paymentMode.forEach(key => {
            dataChart[key] = data.filter(order => order?.payment?.mode?.id === PaymentMode[key]);
        })

        if (isDoughnut && isDoughnut == 'true') {
            return this.orderRepository.findModePayment(query);
        }

        return this.generateChartYearData(dataChart);
    }

    async getTotalSales(query: QueryFilter) {
        query = this.convertFilter(query);
        return await this.orderRepository.getTotalSales(query);
    }


    async getTotalSalesEmployees(query: QueryFilter) {
        query = this.convertFilter(query, ['status']);

        return await this.orderRepository.getTotalSales(query);
    }

    async getCementSalesVolumes(query: QueryFilter) {
        query = this.convertFilter(query);
        const res = await this.orderRepository.getCementSalesVolumes(query);
        return this.groupById(res);

    }
    async getCementSalesVolumesParticular(query: QueryFilter) {
        query = this.convertFilter(query);
        const res = await this.orderSupplierRepository.getCementSalesVolumes(query);
        return this.groupById(res);

    }

    groupById(data) {
        const temp = data.reduce((acc, item) => {
            const id = item._id;
            acc[id] = acc[id] || { ...item, totalValue: 0 };
            acc[id].totalValue += item.totalValue;
            return acc;
        }, {});

        return Object.values(temp);
    }

    async getRankings(query: QueryFilter) {
        query = this.convertFilter(query);
        return await this.orderRepository.getRankingsByValue(query);
    }

    async getTotalUsers(query: QueryFilter) {
        //TODO: add default date here
        query = this.convertFilter(query);
        delete query?.status
        let dataUserCategories = await this.userRepository.getUsersByCategories(query);
        const dataUserCompanyCategories = await this.userRepository.getUsersByCompanyCategories(query)
        dataUserCategories = dataUserCategories.concat(dataUserCompanyCategories);
        const totalUsers = await this.userRepository.getTotalUsers(query);
        return { totalUsers, dataUserCategories };
    }

    async getTotalUsersEmployees(query: QueryFilter) {
        query = this.convertFilter(query);
        delete query?.created_at;
        delete query?.status;

        const totalUsersPromises = [
            this.userRepository.baseAggregation({ filter: query }, 'employeeType'),
            this.userRepository.baseAggregation({ filter: { ...query, isValidated: false, enable: false } }, 'employeeType'),
            this.userRepository.baseAggregation({ filter: { ...query, isRetired: true, isValidated: false, enable: false } }, 'employeeType'),

        ];

        const [totalUsers, totalUsersNotValidate, totalUsersRetired] = await Promise.all(totalUsersPromises);

        return { totalUsers, totalUsersNotValidate, totalUsersRetired };
    }


    async getQuantitiesEvolutionOrderRetail(query: QueryFilter) {
        try {
            query = convertFilter(query);
            if ('user' in query) {
                const userId = query.user;
                query["$or"] = [
                    { "user": userId },
                    { "user._id": userId }
                ]
                delete query.user;
            }

            if ('user' in query) {
                const userId = query.user;
                query["$or"] = [
                    { "user": userId },
                    { "user._id": userId }
                ]
                delete query.user;
            }

            const dataChart = await this.orderRetailRepository.getQuantityEvolutionOrdersRetail(query);
            const dataYear = ReportingHelpers.generateChartYearDataForRetails(dataChart);
            return { dataYear };
        } catch (error) {
            throw error;
        }

    }

    async getSupplierByPoints(query: QueryFilter) {
        query = this.convertFilter(query);
        delete query.status;
        const result = await this.userRepository.getSupplierByPoints(query);
        return result;
    }


    async getDistributorsByVolume(query: QueryFilter) {
        query = this.convertFilter(query);
        return await this.scannerDataRepository.getDistributorsByTotalBags(query);
    }

    async getQuantitiesOrderSupplier(query: QueryFilter) {
        query = this.convertFilter(query);

        const [totalOrderedQunatity, totalOrder, totalOrderValidated, totalOrderPending, totalOrderRejected] = await Promise.all([
            this.scannerDataRepository.getProductsByTotalQuantity(query),
            this.scannerDataRepository.count({ ...query, status: { "$in": [OrderStatus.CREATED, OrderStatus.PAID, OrderStatus.VALIDATED, OrderStatus.CREDIT_REJECTED] } }),
            this.scannerDataRepository.count({ ...query, status: OrderStatus.VALIDATED }),
            this.scannerDataRepository.count({ ...query, status: OrderStatus.CREATED }),
            this.scannerDataRepository.count({ ...query, status: OrderStatus.CREDIT_REJECTED })
        ]);

        const totalOrderedQuantitySum = totalOrderedQunatity?.reduce((sum, item) => sum + item.totalQuantity, 0) || 0;

        return {
            totalOrderedQunatity,
            totalOrderedQuantitySum,
            totalOrder,
            totalOrderValidated,
            totalOrderPending,
            totalOrderRejected
        };
    }

    async getQuantitiesByProduct(query: QueryFilter) {
        query = this.convertFilter(query);

        return await this.scannerDataRepository.getProductsByTotalQuantity(query);
    }

    async getEvolutionPoints(query: QueryFilter) {
        query = this.convertFilter(query);
        const { startDate, endDate } = query;

        const dataChart = await this.scannerDataRepository.getEvolutionPoints(query);
        const dataYear = ReportingHelpers.generateChartYearDataForRetails(dataChart);
        const dataMonth = ReportingHelpers.generateChartMonthData([...dataChart], { startDate, endDate });
        return { dataYear, dataMonth };
    }

    async getProductsEvolutionSupplier(filter: QueryFilter) {
        try {
            filter = this.convertFilter(filter);
            const data = await this.scannerDataRepository.getEvolutionProductSupplier(filter);
            const dataChart = {};

            data.forEach(elt => {
                // If limiting the number of groups to 10 is intentional to get max of products.
                if (Object.keys(dataChart).length < 10) {
                    dataChart[elt.label] = data.filter(item => item.label === elt.label);
                }
            })

            return this.generateChartYearData(dataChart);
        } catch (error) {
            return error;
        }
    }

    async getAvgTimeValidations(query: QueryFilter) {
        query = this.convertFilter(query);
        query = {
            ...query,
            $and: [
                { "dates.paid": { $exists: 1 } },
                { "dates.paid": { $gt: 0 }, },
                { "dates.validated": { $exists: 1 } },
                { "dates.validated": { $gt: 0 }, },
            ],
            status: 300,
        }
        return await this.orderRepository.getAvgTimeValidation(query);
    }

    async getAvgTimeValidationsParticular(query: QueryFilter) {
        query = this.convertFilter(query);
        query = {
            ...query,
            $and: [
                { "dates.paid": { $exists: 1 } },
                { "dates.paid": { $gt: 0 }, },
                { "dates.validated": { $exists: 1 } },
                { "dates.validated": { $gt: 0 }, },
            ],
            status: 300,
        }
        console.log(await this.orderSupplierRepository.getAvgTimeValidation(query));

        return await this.orderSupplierRepository.getAvgTimeValidation(query);
    }

    generatePieChartData(data: any) {
        const res = {};
        for (const key in data) {
            const value = data[key].length;

            res[key] = value;
        }

        return res;
    }

    generateChartYearData(data: any) {
        const res = {};
        for (const key in data) {
            const Jan = (data[key].filter(elt => elt?.monthOfYear == 1)).length;

            const Fev = (data[key].filter(elt => elt?.monthOfYear == 2)).length;

            const Mar = (data[key].filter(elt => elt?.monthOfYear == 3)).length;

            const Avr = (data[key].filter(elt => elt?.monthOfYear == 4)).length;

            const Mai = (data[key].filter(elt => elt?.monthOfYear == 5)).length;

            const Jun = (data[key].filter(elt => elt?.monthOfYear == 6)).length;

            const Jul = (data[key].filter(elt => elt?.monthOfYear == 7)).length;

            const Aou = (data[key].filter(elt => elt?.monthOfYear == 8)).length;

            const Sep = (data[key].filter(elt => elt?.monthOfYear == 9)).length;

            const Oct = (data[key].filter(elt => elt?.monthOfYear == 10)).length;

            const Nov = (data[key].filter(elt => elt?.monthOfYear == 11)).length;

            const Dec = (data[key].filter(elt => elt?.monthOfYear == 12)).length;
            res[key] = [Jan, Fev, Mar, Avr, Mai, Jun, Jul, Aou, Sep, Oct, Nov, Dec];
        }
        return res;
    }


    generateChartYearDataValue(data: any) {
        const res: any = {};
        for (const key in data) {
            const Jan = (data[key].find(elt => elt?.monthOfYear == 1))?.totalValue ?? 0;

            const Fev = (data[key].find(elt => elt?.monthOfYear == 2))?.totalValue ?? 0;

            const Mar = (data[key].find(elt => elt?.monthOfYear == 3))?.totalValue ?? 0;

            const Avr = (data[key].find(elt => elt?.monthOfYear == 4))?.totalValue ?? 0;

            const Mai = (data[key].find(elt => elt?.monthOfYear == 5))?.totalValue ?? 0;

            const Jun = (data[key].find(elt => elt?.monthOfYear == 6))?.totalValue ?? 0;

            const Jul = (data[key].find(elt => elt?.monthOfYear == 7))?.totalValue ?? 0;

            const Aou = (data[key].find(elt => elt?.monthOfYear == 8))?.totalValue ?? 0;

            const Sep = (data[key].find(elt => elt?.monthOfYear == 9))?.totalValue ?? 0;

            const Oct = (data[key].find(elt => elt?.monthOfYear == 10))?.totalValue ?? 0;

            const Nov = (data[key].find(elt => elt?.monthOfYear == 11))?.totalValue ?? 0;

            const Dec = (data[key].find(elt => elt?.monthOfYear == 12))?.totalValue ?? 0;
            res[key] = [Jan, Fev, Mar, Avr, Mai, Jun, Jul, Aou, Sep, Oct, Nov, Dec];
        }
        return res;
    }

    calculpercent(amount: number, amountTotale: number) {
        const percentage = ((amount / amountTotale) * 100);
        return Number(Math.round(percentage + 'e' + 1 as any) + 'e-' + 1);
    }

    convertFilter(fields: QueryFilter, keyToDineds = []) {
        const { filter } = convertParams({ filter: fields });

        const year = parseInt(filter.startDate?.split('-')[0]);
        const month = parseInt(filter.startDate?.split("-")[1]);
        const startDay = parseInt(filter.startDate?.split("-")[2]);
        const endDay = parseInt(filter.endDate?.split("-")[2]);
        // if ((startDay <= 0 || endDay <= 0) || (month <= 6 && month % 2 === 0 && (startDay > 30 || endDay > 30))
        //     || (month > 6 && month % 2 !== 0 && (startDay > 30 || endDay > 30)) || (startDay > 31 || endDay > 31)) {
        //     throw new HttpException(`The Query date format is invalid; The number of days is incorrect based on the month.`, HttpStatus.BAD_REQUEST);
        // }

        if (month == 2 && ((0 == year % 4) && (0 != year % 100) || (0 == year % 400)) && (startDay > 29 || endDay > 29)) {
            throw new HttpException(`The Query date format is invalid; The number of days is incorrect based on the month.`, HttpStatus.BAD_REQUEST);
        }

        if (moment(filter.startDate).valueOf() > moment(filter?.endDate).valueOf()) {
            throw new HttpException(`The start date should not be greater than end date.`, HttpStatus.BAD_REQUEST);
        }

        filter['created_at'] = {
            $gte: moment((filter?.startDate || new Date()), 'YYYY-MM-DD').startOf(filter?.startDate ? 'day' : 'year').valueOf(),
            $lte: moment(filter?.endDate || new Date(), 'YYYY-MM-DD').endOf(filter?.startDate ? 'day' : 'year').valueOf()
        }


        if (filter['items.product._id']) filter['items.product._id'] = new ObjectId(filter['items.product._id']);
        if (filter['user._id']) filter['user._id'] = new ObjectId(filter['user._id']);
        if (filter['company._id']) filter['company._id'] = new ObjectId(filter['company._id']);

        delete filter?.startDate;
        delete filter?.endDate;
        filter.status ??= { "$in": [OrderStatus.VALIDATED] }

        keyToDineds.forEach(key => delete filter[key]);

        return filter;
    }
}