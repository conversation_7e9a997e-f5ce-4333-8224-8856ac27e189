import { UserRepository } from '@la-pasta-module/users/repository';
import { Injectable } from '@nestjs/common';
import { convertFilter } from '@la-pasta/common';

@Injectable()
export class QrScanReportService {
    constructor(
        private readonly userRepository: UserRepository) { }

    // async getQrScanReport(query: QueryFilter) {
    //     return await this.qrScanReportRepository.findAll(query);
    // }

    async getQrScanReportParticular(query: QueryFilter) {
        query = convertFilter(query);
        delete query.status;
        const result = await this.userRepository.getSupplierByPoints(query);
        return result;
    }
}
