import { Injectable } from '@nestjs/common';
import { BaseRepository, t } from '@la-pasta/common';
import { ObjectId, UpdateResult } from 'mongodb';
import { UserCategory } from '../entities/user.entity';
@Injectable()
export class UserRepository extends BaseRepository {
  constructor() {
    super();
  }

  toBoolean(value: any): boolean {
    if (typeof value === "boolean") return value; // Si c'est déjà un booléen, on le garde tel quel
    if (typeof value === "string") return value.toLowerCase() === "true"; // "true" → true, "false" → false
    if (typeof value === "number") return value === 1; // 1 → true, 0 → false
    return false; // Par défaut, tout autre cas est considéré comme false
  }

  async getUsersByCategories(query: QueryFilter) {
    const aggregateExpressions = [];
    const matchExpression = {
      $match: {
        ...query,
        company: { $exists: false },
      },
    };
    aggregateExpressions.push(matchExpression);

    const groupExpression = {
      $group: {
        _id: '$category',
        totalUser: {
          $sum: 1,
        },
      },
    };
    aggregateExpressions.push(groupExpression);

    return (await this.getCollection())
      .aggregate(aggregateExpressions)
      .toArray();
  }

  async getUsersByCategoriesEmployees(query: QueryFilter) {
    const aggregateExpressions = [];
    const matchExpression = {
      $match: {
        ...query,
      },
    };
    aggregateExpressions.push(matchExpression);

    const groupExpression = {
      $group: {
        _id: '$employeeType',
        totalUser: {
          $sum: 1,
        },
      },
    };
    aggregateExpressions.push(groupExpression);

    return (await this.getCollection())
      .aggregate(aggregateExpressions)
      .toArray();
  }

  async getParticularUserByCommercialId(query: QueryFilter) {
    const aggregateExpression = [];
    // Vérification et construction du matchExpression
    const matchExpression: any = { $match: { ...query?.filter } };

    if (Object.keys(matchExpression.$match).length > 0) {
      aggregateExpression.push(matchExpression);
    }

    // Étape de comptage des utilisateurs filtrés
    aggregateExpression.push({
      $count: "totalParticularUserAssociated"
    });

    // Exécuter l'agrégation et retourner le résultat
    const result = await (await this.getCollection())
      .aggregate(aggregateExpression)
      .toArray();


    return result.length > 0 ? result[0] : { totalParticularUserAssociated: 0 };
  }

  async getUsersByCompanyCategories(query: QueryFilter) {
    const aggregateExpressions = [];
    const matchExpression = {
      $match: {
        ...query,
        company: { $exists: true },
      },
    };
    aggregateExpressions.push(matchExpression);

    const groupExpression = {
      $group: {
        _id: '$company.category',
        totalUser: {
          $sum: 1,
        },
      },
    };
    aggregateExpressions.push(groupExpression);

    return (await this.getCollection())
      .aggregate(aggregateExpressions)
      .toArray();
  }

  async getTotalUsers(query: QueryFilter) {
    return await this.count(query);
  }

  async updateUserPoints(userId: string, points: number,): Promise<UpdateResult> {

    return (await this.getCollection()).updateOne(
      { _id: new ObjectId(userId) },
      { $inc: { points } },
    );
  }

  async updateUserValidatePoints(userId: string, points: number,): Promise<UpdateResult> {

    return (await this.getCollection()).updateOne(
      { _id: new ObjectId(userId) },
      { $inc: { 'points.validate':points } },
    );
  }

  async getSupplierByPoints(query: QueryFilter) {
    const offset = (query.offset) ?? 0;
    const limit = (query.limit) ?? 10;

    const matchExpression = {
      $match: {
        ...query,
        category: UserCategory.Particular,
        "points.validate": { $gte: 0 }
      },
    };

    const projectExpression = {
      $project: {
        _id: 0,
        clientName: { $ifNull: ["$socialReason", "$firstName"] },
        totalPoints: "$points.validate",
      },
    };

    const sortExpression = { $sort: { totalPoints: -1 } };

    const result = await (await this.getCollection())
      .aggregate([matchExpression, projectExpression, sortExpression])
      .skip(offset)
      .limit(limit)
      .toArray();

    return result;
  }
}

