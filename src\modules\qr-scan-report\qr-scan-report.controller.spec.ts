import { Test, TestingModule } from '@nestjs/testing';
import { QrScanReportController } from './qr-scan-report.controller';

describe('QrScanReportController', () => {
  let controller: QrScanReportController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [QrScanReportController],
    }).compile();

    controller = module.get<QrScanReportController>(QrScanReportController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
