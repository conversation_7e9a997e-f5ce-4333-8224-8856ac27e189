import { LoyaltyProgramService } from './../loyalty-program/loyalty-program.service';
import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { BaseService, convertFilter, convertParams, setDateFilter, setResponse, t } from '@la-pasta/common';
import { ScannerDataRepository } from './repository';
import { OrderStatus } from '@la-pasta-module/order';
import { ClientProxy } from '@nestjs/microservices';
import { OrderSupplier } from '@la-pasta-module/order-supplier/entities/order-supplier.entity';
import { CreateScannerDataDto } from './dto/create-scanner-data.dto';
import { CartItem } from '@la-pasta-module/price/compute_price';
import { UserEntity } from '@la-pasta-module/loyalty-program/entities';
import { ReturnDocument } from 'mongodb';

@Injectable()
export class ScannerDataService extends BaseService {
  constructor(
    private scannerDataRepository: ScannerDataRepository,
    private loyaltyProgramService: LoyaltyProgramService,
    @Inject('QUEUE') private readonly queueClient: ClientProxy
  ) {
    super(scannerDataRepository)
  }

  async createScannerData(scannerData: CreateScannerDataDto) {

    // scannerData.status = OrderStatus.VALIDATED

    // await this.loyaltyProgramService.setProgramFidelity(scannerData as unknown as OrderSupplier)

    const result = await this.create(scannerData);

    const pointsGenerated = this.loyaltyProgramService.calculateTotalPointsOrder(scannerData?.cart?.items as CartItem[], scannerData.user as UserEntity);

    try {
      this.queueClient.emit("sms_received",
        {
          receiver: scannerData.user.tel, message: `Félicitations ! Vous avez validé 
          ${pointsGenerated} points dans notre programme.
        \n  Merci pour votre fidélité et votre confiance.` });

    } catch (error) {
      this.logger.error("Error while sending sms", error)
    }

    return setResponse(HttpStatus.CREATED, t('SCANNER_DATA_CREATE'), result.data);


  }

  async getVolumeOrderByParticularClient(query: QueryOptions): Promise<any> {

    query = convertParams(query);
    if ('startDate' in query?.filter || 'endDate' in query?.filter) {
      query = setDateFilter(query);
    }
    const data = await this.scannerDataRepository.getVolumeOrderByParticularClient(query);
    return data;
  }

}
