import { UserCategory } from "@la-pasta-module/users";
import { BaseRepository, ReportingHelpers } from "@la-pasta/common";
import { ObjectId } from "bson";
import { Types } from "mongoose";

export class ScannerDataRepository extends BaseRepository {

    constructor() {
        super()
    }

    monthOfYear = {
        $dateToString: {
            format: '%m',
            date: { $toDate: { $toLong: '$created_at' } }
        }
    };

    readonly PACKAGING_VALUE_50 = 50;
    readonly PACKAGING_VALUE_25 = 25;
    readonly PACKAGING_VALUE_5 = 5;

    readonly POINTS_50 = 10;
    readonly POINTS_25 = 5;
    readonly POINTS_5 = 1;

    async getEvolutionSalePaymentForParticular(query: QueryFilter): Promise<unknown[]> {

        if (query['user._id'] instanceof ObjectId) {
            query['user._id'] = query['user._id'].toString();
        }

        const aggregateExpressions = [
            { $match: { ...query } },
            { $unwind: { path: "$cart.items" } },

            {
                $project: {
                    monthOfYear: this.monthOfYear,
                    packagingValue: "$cart.items.packaging.unit.value",
                    quantity: "$cart.items.quantity"
                }
            },

            {
                $addFields: {
                    points: {
                        $multiply: [
                            "$quantity",
                            {
                                $switch: {
                                    branches: [
                                        { case: { $eq: ["$packagingValue", this.PACKAGING_VALUE_50] }, then: this.POINTS_50 },
                                        { case: { $eq: ["$packagingValue", this.PACKAGING_VALUE_25] }, then: this.POINTS_25 },
                                        { case: { $eq: ["$packagingValue", this.PACKAGING_VALUE_5] }, then: this.POINTS_5 }            
                                    ],
                                    default: 0
                                }
                            }
                        ]
                    }
                }
            },

            {
                $group: {
                    _id: "$monthOfYear",
                    totalPoints: { $sum: "$points" }
                }
            },

            {
                $project: {
                    monthOfYear: "$_id",
                    _id: 0,
                    totalValue: "$totalPoints"
                }
            },

            { $sort: { totalValue: -1 } }
        ];

        return await this.findAllAggregate(aggregateExpressions);
    }

    async getEvolutionProduct(query: QueryFilter): Promise<unknown[]> {
        const aggregateExpressions = [];

        if (query['user._id'] instanceof ObjectId) {
            query['user._id'] = query['user._id'].toString();
        }

        const unwindExpressions = {
            $unwind: { path: "$cart.items" }
        }
        aggregateExpressions.push(unwindExpressions);

        const matchExpression = {
            $match: { ...query }
        };
        aggregateExpressions.push(matchExpression);

        const addFieldsExpression = {
            $addFields: { label: "$cart.items.product.label", quantity: "$cart.items.quantity" }
        };
        aggregateExpressions.push(addFieldsExpression);

        const projectExpression = {
            $project: {
                monthOfYear: this.monthOfYear,
                label: 1,
                quantity: 1,
                _id: 0
            }
        };
        aggregateExpressions.push(projectExpression);

        return await this.findAllAggregate(aggregateExpressions);
    }

    async getRepartitionProduct(query: QueryFilter): Promise<unknown[]> {
        const aggregateExpressions = [];
        const unwindExpressions = {
            $unwind: { path: "$cart.items" }
        }
        aggregateExpressions.push(unwindExpressions);

        const matchExpression = {
            $match: { ...query }
        };
        aggregateExpressions.push(matchExpression);

        const groupExpression = {
            $group: {
                _id: '$cart.items.product.label',
                total: {
                    $sum:
                        "$cart.items.quantity",
                },
            }
        };
        aggregateExpressions.push(groupExpression);

        const projectExpression = {
            $project: {
                "label": "$_id",
                "total": '$total',
                _id: 0
            }
        };

        aggregateExpressions.push(projectExpression);

        return await this.findAllAggregate(aggregateExpressions);
    }

    async getVolumeOrderByParticularClient(query: QueryFilter): Promise<unknown[]> {
        const aggregateExpressions = [
            {
                $unwind: {
                    'path': '$cart.items'
                }
            }, {
                $match: {
                    ...query.filter,
                }
            }, {
                $group: {
                    '_id': '$user._id',
                    'firstName': {
                        '$first': '$user.firstName'
                    },
                    'lastName': {
                        '$first': '$user.lastName'
                    },
                    'totalOrder': {
                        '$sum': 1
                    },
                    'totalQuantity': {
                        '$sum': {
                            '$sum': '$cart.items.quantity'
                        }
                    }
                }
            }, {
                $addFields: {
                    '_id': {
                        '$toObjectId': '$_id'
                    }
                }
            }, {
                $project: {
                    'firstName': 1,
                    'lastName': 1,
                    'totalOrder': 1,
                    'totalQuantity': 1
                }
            }
        ];

        return (await this.getCollection())
            .aggregate(aggregateExpressions)
            .toArray();

    }

    async getEvolutionOrderByScannerOrder(query: QueryFilter): Promise<unknown[]> {
        const aggregateExpressions = [];

        if (query['user._id'] instanceof ObjectId) {
            query['user._id'] = query['user._id'].toString();
        }

        aggregateExpressions.push({ $unwind: { path: "$cart.items" } });

        aggregateExpressions.push({ $match: { ...query } });

        aggregateExpressions.push({
            $addFields: {
                label: "$cart.items.product.label",
                quantity: "$cart.items.quantity"
            }
        });

        aggregateExpressions.push({
            $project: {
                monthOfYear: this.monthOfYear,
                label: 1,
                quantity: 1,
                _id: 0
            }
        });

        return await this.findAllAggregate(aggregateExpressions);
    }

    async getDistributorsByTotalBags(query: QueryFilter) {
        const matchExpression = {
            $match: {
                ...query,
                "cart.items": { $exists: true, $ne: [] }
            }
        };

        const unwindExpression = { $unwind: "$cart.items" };

        const groupExpression = {
            $group: {
                _id: {
                    supplierId: "$supplier._id",
                    supplierName: "$supplier.name"
                },
                totalBags: {
                    $sum: { $ifNull: ["$cart.items.quantity", 0] }
                }
            }
        };

        const projectExpression = {
            $project: {
                _id: 0,
                supplierName: {
                    $ifNull: ["$_id.supplierName", "Unknown"]
                },
                totalBags: 1
            }
        };

        const sortExpression = { $sort: { totalBags: -1 } };

        return (await this.getCollection())
            .aggregate([
                matchExpression,
                unwindExpression,
                groupExpression,
                projectExpression,
                sortExpression
            ])
            .toArray();
    }



    async getProductsByTotalQuantity(query: QueryFilter) {
        const matchExpression = {
            $match: {
                ...query,
                "cart.items": { $exists: true }
            }
        };

        const unwindExpression = {
            $unwind: "$cart.items"
        };

        const groupExpression = {
            $group: {
                _id: {
                    productId: "$cart.items.product._id",
                    status: "$status"
                },
                productName: { $first: "$cart.items.product.label" },
                totalQuantity: {
                    $sum: "$cart.items.quantity",
                }
            }
        };

        const sortExpression = { $sort: { totalQuantity: -1 } };

        const projectExpression = {
            $project: {
                _id: 0,
                productId: "$_id.productId",
                status: "$_id.status",
                productName: 1,
                totalQuantity: 1
            }
        };

        return (await this.getCollection())
            .aggregate([
                matchExpression,
                unwindExpression,
                groupExpression,
                sortExpression,
                projectExpression
            ])
            .toArray();
    }

    async getEvolutionPoints(query: QueryFilter) {

        const aggregateExpression = ReportingHelpers.getChartBaseQuery(query);
        return await this.findAllAggregate(aggregateExpression);
    }

    async getEvolutionProductSupplier(query: QueryFilter) {
        const aggregateExpressions = [
            { $unwind: { path: "$cart.items" } },

            {
                $match: {
                    ...query,
                    'cart.items': { '$exists': true },
                },
            },
            {
                $addFields: {
                    label: "$cart.items.product.label",
                },
            },
            {
                $project: {
                    monthOfYear: this.monthOfYear,
                    label: 1,
                    _id: 0
                },
            },
        ];

        return await (await this.getCollection()).aggregate(aggregateExpressions).toArray();
    }
}